import React, { useState, useEffect, FormEvent } from "react";
import {
  Pick,
  OriginData,
} from "../../pages/ProjectParlayDashboard/ProjectParlayDashboard"; // Assuming types are exported from here

const ALL_PICK_ORIGINS = [
  "ChalkBoardPI",
  "HarryLock",
  "DanGamblePOD",
  "DanGambleAIEdge",
  "GameScript",
  "Winible",
  "DoberMan",
  "JoshMiller",
  "Me",
];

const STAT_TYPE_OPTIONS: Record<string, string[]> = {
  MLB: [
    "K's",
    "Total Bases",
    "Hitter Fpts",
    "Pitcher Fpts",
    "H+R+RBIs",
    "HR's",
    "Hits Allowed",
    "SB's",
    "Doubles",
    "Walks Allowed",
    "Singles",
    "Walks",
    "Hits",
    "Earned Runs Allowed",
    "RBI's",
    "Runs",
  ],
  NBA: [
    "Points",
    "PRA",
    "Rebounds",
    "Assists",
    "3-pt's",
    "P+A",
    "FG Made",
    "D Rebounds",
    "Fpts",
    "R+A",
    "O Rebounds",
    "3-pt's Attempted",
    "FT's Made",
    "FG Attempted",
    "P+R",
    "Dunks",
    "Blocks",
    "Steals",
    "B+S",
    "Turnovers",
  ],
  NHL: [
    "Goals",
    "Assists",
    "Points",
    "Shots on Goal",
    "Power Play Points",
    "Hits",
    "Blocked Shots",
    "Penalty Minutes",
    "Faceoff Wins",
    "Time on Ice",
    "Plus/Minus",
    "Shots",
    "Power Play Goals",
    "Shorthanded Goals",
    "Game-Winning Goals",
    "Overtime Goals",
    "First Goal",
    "Last Goal",
    "Anytime Goal",
    "Multi-Point Game",
    "Multi-Goal Game",
  ],
  NFL: [
    "Passing Yards",
    "Passing Touchdowns",
    "Interceptions Thrown",
    "Completions",
    "Pass Attempts",
    "Rushing Yards",
    "Rushing Touchdowns",
    "Carries",
    "Receiving Yards",
    "Receptions",
    "Receiving Touchdowns",
    "Targets",
    "Longest Reception",
    "Longest Rush",
    "Total Touchdowns",
    "Anytime Touchdown",
    "First Touchdown",
    "Last Touchdown",
    "Field Goals Made",
    "Field Goals Attempted",
    "Extra Points Made",
    "Tackles",
    "Sacks",
    "Interceptions",
    "Passes Defended",
    "Fumbles Recovered",
    "Forced Fumbles",
    "Defensive Touchdowns",
  ],
};

// FormData for the form state
export interface PickFormSubmitData {
  name: string;
  odds: string;
  pick_origin: OriginData[];
  league: string[]; // Backend expects an array, even if single
  reusable: boolean;
  capital_limit: number;
  mutual_exclusion: number;
  pick_type: string;
  player_team?: string;
  stat_type?: string;
  prediction: number; // 0 for Lower, 1 for Higher
  // For edits, we might need the original ID
  id?: string | number;
}

interface PickOriginState {
  name: string;
  selected: boolean;
  confidence: string; // Keep as string for input field
  // prediction is global in this form
}

interface PickFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (formData: PickFormSubmitData) => void;
  initialName?: string; // Name passed for new pick creation
  editingPick?: Pick | null; // Full pick object for editing
}

const PickForm: React.FC<PickFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  initialName = "",
  editingPick,
}) => {
  const [name, setName] = useState(initialName);
  const [odds, setOdds] = useState("");
  const [pickOrigins, setPickOrigins] = useState<PickOriginState[]>(() =>
    ALL_PICK_ORIGINS.map((origin) => ({
      name: origin,
      selected: false,
      confidence: "",
    }))
  );
  const [globalPrediction, setGlobalPrediction] = useState<"1" | "0">("1"); // '1' for Higher, '0' for Lower
  const [league, setLeague] = useState(""); // Single league selection
  const [pickType, setPickType] = useState<"Prop" | "Custom" | "">("");
  const [playerTeam, setPlayerTeam] = useState("");
  const [statType, setStatType] = useState("");
  const [reusable, setReusable] = useState(true);
  const [mutualExclusion, setMutualExclusion] = useState("-1");
  const [capitalLimit, setCapitalLimit] = useState("0");

  const isEditing = !!editingPick;

  useEffect(() => {
    if (editingPick) {
      setName(editingPick.name);
      setOdds(String(editingPick.odds));
      setGlobalPrediction(String(editingPick.prediction) as "1" | "0");
      setLeague(editingPick.league[0] || ""); // Assuming single league from array
      setPickType(editingPick.pick_type as "MoneyLine" | "Prop" | "");
      setPlayerTeam(editingPick.player_team || "");
      setStatType(editingPick.stat_type || "");
      setReusable(editingPick.reusable);
      setMutualExclusion(String(editingPick.mutual_exclusion));
      setCapitalLimit(String(editingPick.capital_limit));

      const originsFromPick = Array.isArray(editingPick.pick_origin)
        ? editingPick.pick_origin
        : [];
      setPickOrigins(
        ALL_PICK_ORIGINS.map((originName) => {
          const existingOrigin = originsFromPick.find(
            (po) => po.name === originName
          );
          return {
            name: originName,
            selected: !!existingOrigin,
            confidence:
              existingOrigin && existingOrigin.confidence !== null
                ? String(existingOrigin.confidence)
                : "",
          };
        })
      );
    } else {
      // Reset form for new pick (or use initialName)
      setName(initialName || ""); // Use initialName if provided for creation, else empty
      setOdds("");
      setGlobalPrediction("1");
      setLeague("");
      setPickType("");
      setPlayerTeam("");
      setStatType("");
      setReusable(true);
      setMutualExclusion("-1");
      setCapitalLimit("0");
      setPickOrigins(
        ALL_PICK_ORIGINS.map((origin) => ({
          name: origin,
          selected: false,
          confidence: "",
        }))
      );
    }
  }, [editingPick, initialName, isOpen]); // isOpen ensures reset when form reopens for new pick

  const handleOriginChange = (index: number) => {
    const updatedOrigins = [...pickOrigins];
    updatedOrigins[index].selected = !updatedOrigins[index].selected;
    if (!updatedOrigins[index].selected) {
      updatedOrigins[index].confidence = ""; // Clear confidence if deselected
    }
    setPickOrigins(updatedOrigins);
  };

  const handleConfidenceChange = (index: number, value: string) => {
    const updatedOrigins = [...pickOrigins];
    updatedOrigins[index].confidence = value;
    setPickOrigins(updatedOrigins);
  };

  const handlePickTypeChange = (selectedType: "MoneyLine" | "Prop") => {
    setPickType((prev) => (prev === selectedType ? "" : selectedType)); // Toggle or set
    if (selectedType !== "Prop") {
      setPlayerTeam("");
      // setStatType(''); // Optionally clear stat type if not Prop
    }
  };

  const handleLeagueChange = (selectedLeague: string) => {
    setLeague((prev) => (prev === selectedLeague ? "" : selectedLeague));
    setStatType(""); // Reset stat type when league changes
  };

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (!name && !isEditing)
      return alert("Please provide a name for the pick.");
    if (!odds) return alert("Odds are required.");
    if (!pickType) return alert("Pick Type is required.");
    if (!league) return alert("League is required.");

    const selectedOrigins = pickOrigins
      .filter((po) => po.selected)
      .map((po) => ({
        name: po.name,
        confidence: po.confidence ? parseFloat(po.confidence) : null,
        prediction: parseInt(globalPrediction), // Global prediction for all selected
      }));

    if (selectedOrigins.length === 0)
      return alert("At least one Pick Origin must be selected.");
    if (pickType === "Prop" && !statType)
      return alert("Stat Type is required for Prop picks.");

    const formData: PickFormSubmitData = {
      name: name,
      odds: odds,
      pick_origin: selectedOrigins,
      league: league ? [league] : [],
      reusable: reusable,
      capital_limit: parseInt(capitalLimit) || 0,
      mutual_exclusion: parseInt(mutualExclusion),
      pick_type: pickType,
      player_team: pickType === "Prop" ? playerTeam : undefined,
      stat_type: pickType === "Prop" ? statType : undefined,
      prediction: parseInt(globalPrediction),
    };
    if (isEditing && editingPick) {
      formData.id = editingPick.pID; // or editingPick.id, depending on your Pick interface for frontend ID
    }

    onSubmit(formData);
  };

  if (!isOpen) return null;

  const currentStatOptions =
    pickType === "Prop" && league && STAT_TYPE_OPTIONS[league]
      ? STAT_TYPE_OPTIONS[league]
      : [];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-start pt-12 z-50 overflow-y-auto">
      <div className="bg-gray-800 p-6 rounded-lg shadow-xl w-full max-w-2xl max-h-[85vh] overflow-y-auto text-sm">
        <h2 className="text-2xl font-semibold mb-6 text-[#58C612]">
          {isEditing ? "Edit Pick" : "Create Pick"}
        </h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="pickName" className="block text-[#58C612] mb-1">
              Pick Name
            </label>
            <input
              id="pickName"
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full p-2 bg-gray-700 border border-gray-600 rounded focus:ring-[#58C612] focus:border-[#58C612]"
              placeholder={
                isEditing ? "" : "Enter pick name (e.g., Mike Trout 0.5 HR)"
              }
              disabled={isEditing} // Name is often fixed on edit, or pre-filled and editable if that's desired
            />
          </div>

          <div>
            <label className="block text-[#58C612] mb-1">Prediction</label>
            <div className="flex gap-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="globalPrediction"
                  value="1"
                  checked={globalPrediction === "1"}
                  onChange={() => setGlobalPrediction("1")}
                  className="mr-2"
                />{" "}
                Higher
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="globalPrediction"
                  value="0"
                  checked={globalPrediction === "0"}
                  onChange={() => setGlobalPrediction("0")}
                  className="mr-2"
                />{" "}
                Lower
              </label>
            </div>
          </div>

          <div>
            <label htmlFor="odds" className="block text-[#58C612] mb-1">
              Odds
            </label>
            <input
              id="odds"
              type="text"
              value={odds}
              onChange={(e) => setOdds(e.target.value)}
              className="w-full p-2 bg-gray-700 border border-gray-600 rounded"
              placeholder="e.g., 1.85 or -110"
            />
          </div>

          <div>
            <label className="block text-[#58C612] mb-1">Pick Type</label>
            <div className="flex gap-4">
              {["MoneyLine", "Prop"].map((pt) => (
                <label key={pt} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={pickType === pt}
                    onChange={() =>
                      handlePickTypeChange(pt as "MoneyLine" | "Prop")
                    }
                    className="mr-2"
                  />{" "}
                  {pt}
                </label>
              ))}
            </div>
          </div>

          {pickType === "Prop" && (
            <div>
              <label htmlFor="playerTeam" className="block text-[#58C612] mb-1">
                Player Team
              </label>
              <input
                id="playerTeam"
                type="text"
                value={playerTeam}
                onChange={(e) => setPlayerTeam(e.target.value)}
                className="w-full p-2 bg-gray-700 border border-gray-600 rounded"
                placeholder="e.g., Lakers"
              />
            </div>
          )}

          <div>
            <label className="block text-[#58C612] mb-1">Pick Origins</label>
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
              {pickOrigins.map((origin, index) => (
                <div
                  key={origin.name}
                  className="p-2 border border-gray-700 rounded"
                >
                  <label className="flex items-center mb-1">
                    <input
                      type="checkbox"
                      checked={origin.selected}
                      onChange={() => handleOriginChange(index)}
                      className="mr-2"
                    />
                    {origin.name}
                  </label>
                  {origin.selected && (
                    <input
                      type="number"
                      value={origin.confidence}
                      onChange={(e) =>
                        handleConfidenceChange(index, e.target.value)
                      }
                      className="w-full p-1 mt-1 bg-gray-600 border border-gray-500 rounded text-xs"
                      placeholder="Conf % (0-100)"
                      min="0"
                      max="100"
                    />
                  )}
                </div>
              ))}
            </div>
          </div>

          <div>
            <label className="block text-[#58C612] mb-1">League</label>
            <div className="flex flex-wrap gap-2">
              {Object.keys(STAT_TYPE_OPTIONS).map((lg) => (
                <label
                  key={lg}
                  className="flex items-center p-2 border border-gray-700 rounded hover:bg-gray-700 cursor-pointer"
                >
                  <input
                    type="checkbox"
                    checked={league === lg}
                    onChange={() => handleLeagueChange(lg)}
                    className="mr-2"
                  />{" "}
                  {lg}
                </label>
              ))}
            </div>
          </div>

          {pickType === "Prop" && league && currentStatOptions.length > 0 && (
            <div>
              <label className="block text-[#58C612] mb-1">Stat Type</label>
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 max-h-48 overflow-y-auto p-2 border border-gray-700 rounded">
                {currentStatOptions.map((st) => (
                  <label
                    key={st}
                    className="flex items-center p-1 rounded hover:bg-gray-700 cursor-pointer text-xs"
                  >
                    <input
                      type="checkbox"
                      checked={statType === st}
                      onChange={() =>
                        setStatType((prev) => (prev === st ? "" : st))
                      }
                      className="mr-2"
                    />{" "}
                    {st}
                  </label>
                ))}
              </div>
            </div>
          )}
          {pickType === "Prop" &&
            league &&
            currentStatOptions.length === 0 &&
            pickType === "Prop" &&
            league && (
              <p className="text-yellow-500 text-xs mt-1">
                No stat types available for the selected league or pick type
                combination.
              </p>
            )}

          <div className="flex items-center">
            <input
              id="reusable"
              type="checkbox"
              checked={reusable}
              onChange={(e) => setReusable(e.target.checked)}
              className="mr-2 h-4 w-4"
            />
            <label htmlFor="reusable" className="text-[#58C612]">
              Reusable Pick
            </label>
          </div>

          <div>
            <label
              htmlFor="mutualExclusion"
              className="block text-[#58C612] mb-1"
            >
              Mutual Exclusion Group
            </label>
            <select
              id="mutualExclusion"
              value={mutualExclusion}
              onChange={(e) => setMutualExclusion(e.target.value)}
              className="w-full p-2 bg-gray-700 border border-gray-600 rounded"
            >
              <option value="-1">None (-1)</option>
              {Array.from({ length: 10 }, (_, i) => i + 1).map((num) => (
                <option key={num} value={String(num)}>
                  {num}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="capitalLimit" className="block text-[#58C612] mb-1">
              Capital Limit ($)
            </label>
            <input
              id="capitalLimit"
              type="number"
              value={capitalLimit}
              onChange={(e) => setCapitalLimit(e.target.value)}
              min="0"
              className="w-full p-2 bg-gray-700 border border-gray-600 rounded"
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 hover:bg-gray-500 rounded text-white"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-[#58C612] hover:bg-[#58C612] rounded text-black font-semibold"
            >
              {isEditing ? "Save Changes" : "Create Pick"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PickForm;
