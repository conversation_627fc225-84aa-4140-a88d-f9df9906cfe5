import React from "react";
import { getConfidenceColor } from "../../utils/colorUtils";

interface SubparlayPick {
  pID: string | number;
  odds: string;
  confidence?: number;
  name?: string;
  bayesian_conf?: number;
  bayesian_prob?: number;
  logistic_prob?: number;
  capital_limit?: number;
  event_id?: string;
  gameID?: number;
  league?: string;
  reusable?: boolean;
  stat_type?: string;
}

interface SubparlayColumn extends Array<SubparlayPick> {}

interface GeneratedParlaysSectionProps {
  subparlays: SubparlayColumn[];
  isParlaysExpanded: boolean;
  onToggleExpanded: () => void;
}

function GeneratedParlaysSection({
  subparlays,
  isParlaysExpanded,
  onToggleExpanded,
}: GeneratedParlaysSectionProps) {
  if (subparlays.length === 0) {
    return null;
  }

  return (
    <div
      className={`${
        isParlaysExpanded ? "min-h-[40vh]" : "min-h-0"
      } bg-[#051844] flex flex-col items-center ${
        isParlaysExpanded ? "pb-3 sm:pb-6" : "pb-2 sm:pb-3"
      } my-8 sm:my-12 relative overflow-visible`}
    >
      <div className="w-full max-w-7xl mx-auto flex flex-col relative overflow-visible px-4 sm:px-6">
        <div
          className={`${
            isParlaysExpanded ? "pt-3" : "pt-2"
          } flex flex-col relative overflow-visible z-10`}
        >
          <div
            className={`bg-[#233e6c] p-6 cursor-pointer flex items-center justify-between hover:bg-[#1a2d54] transition-colors duration-300 ${
              isParlaysExpanded ? "rounded-t-xl" : "rounded-xl"
            }`}
            onClick={onToggleExpanded}
          >
            <div className="flex items-center gap-3">
              <span
                className={`text-white text-2xl font-bold transition-transform duration-200 ${
                  isParlaysExpanded ? "rotate-180" : ""
                }`}
              >
                ▼
              </span>
              <div>
                <h3 className="text-white text-2xl font-bold">
                  Generated Parlays
                </h3>
                <p className="text-white/70 text-sm mt-1">
                  {subparlays.length} optimized parlay
                  {subparlays.length !== 1 ? "s" : ""} ready
                </p>
              </div>
            </div>
            <div className="text-white/60 text-sm">
              {isParlaysExpanded ? "Click to collapse" : "Click to expand"}
            </div>
          </div>

          {isParlaysExpanded && (
            <div
              className="relative min-h-[400px]"
              style={{ overflow: "visible", paddingTop: "140px" }}
            >
              {/* Background container with rounded corners */}
              <div
                className="absolute inset-0 bg-[#233e6c] rounded-b-xl"
                style={{ top: "0px" }}
              ></div>

              {/* Content container that can overflow */}
              <div
                className="relative p-4 sm:p-6"
                style={{ overflow: "visible", zIndex: 1 }}
              >
                {/* Scrollable container for parlays with tooltip overflow support */}
                <div className="relative" style={{ overflow: "visible" }}>
                  <div
                    className={`${
                      subparlays.length > 6 ? "parlay-scroll" : ""
                    } relative z-[1] pt-16 pb-4`}
                    style={{
                      marginTop: "-6rem",
                      overflowX: subparlays.length > 6 ? "auto" : "visible",
                      overflowY: "visible",
                    }}
                  >
                    <div
                      className="flex gap-8 justify-start relative overflow-visible items-start h-fit min-h-[calc(100%-16px)]"
                      style={{
                        minWidth:
                          subparlays.length > 6
                            ? `${subparlays.length * 192}px`
                            : "auto",
                      }}
                    >
                      {subparlays.map((column, i) => (
                        <div
                          key={i}
                          data-parlay-column={i}
                          className="flex flex-col items-center relative flex-shrink-0 overflow-visible z-[1] w-40 self-start"
                        >
                          <div className="text-white text-lg font-bold mb-4 flex-shrink-0">
                            Parlay {i + 1}
                          </div>
                          <div
                            className={`flex flex-col gap-3 min-w-[160px] relative self-start min-h-0 overflow-x-visible ${
                              column.length > 6
                                ? "overflow-y-auto parlay-scroll max-h-[552px]"
                                : "overflow-visible"
                            }`}
                          >
                            {column.map((pick) => (
                              <div
                                key={pick.pID}
                                className="h-20 w-full rounded-lg border-2 border-white/30 cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-lg hover:border-white/60 relative group z-[1]"
                                style={{
                                  backgroundColor: getConfidenceColor(
                                    pick.confidence
                                  ),
                                }}
                              >
                                {/* Pick content */}
                                <div className="p-3 h-full flex flex-col justify-center items-center text-center">
                                  <div className="text-black font-semibold text-base leading-tight">
                                    {pick.name?.split(" - ")[0] ||
                                      `Pick #${pick.pID}`}
                                  </div>
                                  <div className="text-black/80 text-xs mt-1">
                                    {pick.confidence?.toFixed(0)}% confidence
                                  </div>
                                </div>

                                {/* Simple tooltip with proper positioning */}
                                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-4 py-3 bg-gray-900 text-white text-sm rounded-lg shadow-xl opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none min-w-[280px] z-[99999999]">
                                  <div className="space-y-2">
                                    {/* Player and Bet Info */}
                                    <div className="border-b border-gray-600 pb-2">
                                      <div className="font-bold text-green-400">
                                        {pick.name || `Pick #${pick.pID}`}
                                      </div>
                                      <div className="text-gray-300 text-xs">
                                        {pick.league} • {pick.stat_type}
                                      </div>
                                    </div>

                                    {/* Confidence and Odds */}
                                    <div className="grid grid-cols-2 gap-3">
                                      <div>
                                        <div className="text-white text-xs">
                                          Confidence
                                        </div>
                                        <div className="font-semibold text-white">
                                          {pick.confidence?.toFixed(0)}%
                                        </div>
                                      </div>
                                      <div>
                                        <div className="text-white text-xs">
                                          Odds
                                        </div>
                                        <div className="font-semibold text-white">
                                          {pick.odds}
                                        </div>
                                      </div>
                                    </div>

                                    {/* Probability Analysis */}
                                    <div className="border-t border-gray-600 pt-2">
                                      <div className="text-white text-xs mb-1">
                                        Probability Analysis
                                      </div>
                                      <div className="grid grid-cols-2 gap-2 text-xs">
                                        <div>
                                          <span className="text-white">
                                            Bayesian:
                                          </span>{" "}
                                          <span className="text-white">
                                            {pick.bayesian_prob
                                              ? (
                                                  pick.bayesian_prob * 100
                                                ).toFixed(1)
                                              : "N/A"}
                                            %
                                          </span>
                                        </div>
                                        <div>
                                          <span className="text-white">
                                            Logistic:
                                          </span>{" "}
                                          <span className="text-white">
                                            {pick.logistic_prob
                                              ? (
                                                  pick.logistic_prob * 100
                                                ).toFixed(1)
                                              : "N/A"}
                                            %
                                          </span>
                                        </div>
                                      </div>
                                    </div>

                                    {/* Additional Info */}
                                    <div className="border-t border-gray-600 pt-2 text-xs space-y-1">
                                      <div className="text-white">
                                        Event ID:{" "}
                                        <span className="text-white">
                                          {pick.event_id || "N/A"}
                                        </span>
                                      </div>
                                      <div className="text-white">
                                        Bayesian Conf:{" "}
                                        <span className="text-white">
                                          {pick.bayesian_conf?.toFixed(2) ||
                                            "N/A"}
                                        </span>
                                      </div>
                                      {pick.gameID && pick.gameID !== -1 && (
                                        <div className="text-white">
                                          Game ID:{" "}
                                          <span className="text-white">
                                            {pick.gameID}
                                          </span>
                                        </div>
                                      )}
                                      {pick.capital_limit !== undefined && (
                                        <div className="text-white">
                                          Capital Limit:{" "}
                                          <span className="text-white">
                                            {pick.capital_limit}
                                          </span>
                                        </div>
                                      )}
                                      {pick.reusable !== undefined && (
                                        <div className="text-white">
                                          Reusable:{" "}
                                          <span className="text-white">
                                            {pick.reusable ? "Yes" : "No"}
                                          </span>
                                        </div>
                                      )}
                                    </div>
                                  </div>

                                  {/* Tooltip arrow */}
                                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default GeneratedParlaysSection;
