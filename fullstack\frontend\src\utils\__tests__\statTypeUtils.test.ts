import { normalizeStatType, formatStatTypeForDisplay } from '../dataTransforms';

describe('Stat Type Utilities', () => {
  describe('normalizeStatType', () => {
    it('should convert to lowercase', () => {
      expect(normalizeStatType('POINTS')).toBe('points');
      expect(normalizeStatType('Points')).toBe('points');
      expect(normalizeStatType('points')).toBe('points');
    });

    it('should trim whitespace', () => {
      expect(normalizeStatType('  Points  ')).toBe('points');
      expect(normalizeStatType('\tAssists\n')).toBe('assists');
    });

    it('should handle empty strings', () => {
      expect(normalizeStatType('')).toBe('');
      expect(normalizeStatType('   ')).toBe('');
    });
  });

  describe('formatStatTypeForDisplay', () => {
    it('should format lowercase database values to proper case', () => {
      expect(formatStatTypeForDisplay('points')).toBe('Points');
      expect(formatStatTypeForDisplay('assists')).toBe('Assists');
      expect(formatStatTypeForDisplay('rebounds')).toBe('Rebounds');
      expect(formatStatTypeForDisplay('pra')).toBe('PRA');
    });

    it('should handle legacy uppercase values', () => {
      expect(formatStatTypeForDisplay('POINTS')).toBe('Points');
      expect(formatStatTypeForDisplay('ASSISTS')).toBe('Assists');
      expect(formatStatTypeForDisplay('PTS')).toBe('Points');
      expect(formatStatTypeForDisplay('AST')).toBe('Assists');
    });

    it('should handle mixed case values', () => {
      expect(formatStatTypeForDisplay('Points')).toBe('Points');
      expect(formatStatTypeForDisplay('Assists')).toBe('Assists');
    });

    it('should provide fallback for unknown values', () => {
      expect(formatStatTypeForDisplay('unknown_stat')).toBe('Unknown_stat');
      expect(formatStatTypeForDisplay('CUSTOM_STAT')).toBe('Custom_stat');
    });

    it('should handle empty/null values', () => {
      expect(formatStatTypeForDisplay('')).toBe('Points');
      expect(formatStatTypeForDisplay(null as any)).toBe('Points');
      expect(formatStatTypeForDisplay(undefined as any)).toBe('Points');
    });

    it('should handle special stat types', () => {
      expect(formatStatTypeForDisplay('3-pt\'s')).toBe('3-pt\'s');
      expect(formatStatTypeForDisplay('p+a')).toBe('P+A');
      expect(formatStatTypeForDisplay('fg made')).toBe('FG Made');
    });
  });
});
