// Defined in: ai_docs/event-date-handicapper-filter/plan.md
import type { Expert } from "../types/expert";

/**
 * Fetches a list of unique experts who have predictions for a given date.
 *
 * @param date - The target date in ISO format (YYYY-MM-DD).
 * @returns A promise that resolves to an array of Expert objects.
 */
export const getExpertsByDate = async (date: string): Promise<Expert[]> => {
  try {
    const response = await fetch(`/api/experts_by_date?date=${date}`);

    if (!response.ok) {
      throw new Error(
        `Failed to fetch experts: ${response.status} ${response.statusText}`
      );
    }

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || "Failed to fetch experts");
    }

    // Convert the expert names array to Expert objects with id and name
    return data.experts.map((expertName: string, index: number) => ({
      id: `expert_${index + 1}`,
      name: expertName,
    }));
  } catch (error) {
    console.error(`❌ Error fetching experts for ${date}:`, error);

    // Return empty array instead of mock data to indicate no experts available
    // This will show the user that no experts have predictions for this date
    return [];
  }
};
