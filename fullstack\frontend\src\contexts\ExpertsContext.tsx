import React, { createContext, useContext, useState, useCallback, useMemo } from 'react';
import type { Expert } from '../types/expert';

interface ExpertsContextType {
  expertsByDate: Record<string, Expert[]>;
  getExperts: (date: string) => Promise<Expert[]>;
  preload: (date: string) => void;
}

const ExpertsContext = createContext<ExpertsContextType | undefined>(undefined);

export const ExpertsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [expertsByDate, setExpertsByDate] = useState<Record<string, Expert[]>>({});

  const fetchExperts = useCallback(async (date: string): Promise<Expert[]> => {
    // Avoid duplicate fetches
    if (expertsByDate[date]) return expertsByDate[date];

    try {
      const res = await fetch(`/api/experts_by_date?date=${date}`);
      if (!res.ok) throw new Error('Failed to fetch experts');
      const data = await res.json();
      const experts: Expert[] = (data.experts || []).map((name: string, idx: number) => ({ id: `${idx}`, name }));
      setExpertsByDate(prev => ({ ...prev, [date]: experts }));
      return experts;
    } catch (err) {
      console.error('Error fetching experts for', date, err);
      throw err;
    }
  }, [expertsByDate]);

  const getExperts = useCallback((date: string) => fetchExperts(date), [fetchExperts]);
  const preload = useCallback((date: string) => {
    fetchExperts(date).catch(() => {});
  }, [fetchExperts]);

  const value = useMemo(() => ({ expertsByDate, getExperts, preload }), [expertsByDate, getExperts, preload]);

  return <ExpertsContext.Provider value={value}>{children}</ExpertsContext.Provider>;
};

export const useExperts = (): ExpertsContextType => {
  const ctx = useContext(ExpertsContext);
  if (!ctx) throw new Error('useExperts must be used inside ExpertsProvider');
  return ctx;
};
