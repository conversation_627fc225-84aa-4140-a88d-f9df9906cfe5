export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

export interface CacheConfig {
  // How long cached data is considered fresh (in milliseconds)
  freshDuration: number;
  // Maximum age before data is completely invalid (in milliseconds)
  maxAge: number;
  // Version for cache invalidation when data structure changes
  version: string;
}

const DEFAULT_CONFIG: CacheConfig = {
  freshDuration: 5 * 60 * 1000, // 5 minutes
  maxAge: 30 * 60 * 1000, // 30 minutes
  version: "1.0.0",
};

class SmartCache {
  private storageKey: string;
  private config: CacheConfig;

  constructor(storageKey: string, config: Partial<CacheConfig> = {}) {
    this.storageKey = storageKey;
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Generate cache key with date parameter
   */
  private getCacheKey(identifier: string = "default"): string {
    return `${this.storageKey}_${identifier}_v${this.config.version}`;
  }

  /**
   * Store data in cache with metadata
   */
  set<T>(data: T, identifier: string = "default"): void {
    try {
      const now = Date.now();
      const cacheEntry: CacheEntry<T> = {
        data,
        timestamp: now,
        expiresAt: now + this.config.maxAge,
      };

      const cacheKey = this.getCacheKey(identifier);
      localStorage.setItem(cacheKey, JSON.stringify(cacheEntry));

      console.log(`📦 Cache SET: ${cacheKey}`, {
        dataSize: JSON.stringify(data).length,
        expiresIn: Math.round(this.config.maxAge / 1000 / 60) + " minutes",
      });
    } catch (error) {
      console.warn("Failed to cache data:", error);
      // Gracefully handle storage quota exceeded
      this.cleanup();
    }
  }

  /**
   * Get data from cache with freshness information
   */
  get<T>(identifier: string = "default"): {
    data: T | null;
    isFresh: boolean;
    isExpired: boolean;
    age: number;
  } {
    try {
      const cacheKey = this.getCacheKey(identifier);
      const cachedStr = localStorage.getItem(cacheKey);

      if (!cachedStr) {
        console.log(`📦 Cache MISS: ${cacheKey}`);
        return { data: null, isFresh: false, isExpired: true, age: 0 };
      }

      const cached: CacheEntry<T> = JSON.parse(cachedStr);
      const now = Date.now();
      const age = now - cached.timestamp;
      const isFresh = age < this.config.freshDuration;
      const isExpired = now > cached.expiresAt;

      if (isExpired) {
        console.log(`📦 Cache EXPIRED: ${cacheKey}`, {
          age: Math.round(age / 1000 / 60) + " minutes ago",
        });
        this.delete(identifier);
        return { data: null, isFresh: false, isExpired: true, age };
      }

      console.log(`📦 Cache HIT: ${cacheKey}`, {
        isFresh,
        age: Math.round(age / 1000 / 60) + " minutes ago",
      });

      return { data: cached.data, isFresh, isExpired: false, age };
    } catch (error) {
      console.warn("Failed to read from cache:", error);
      return { data: null, isFresh: false, isExpired: true, age: 0 };
    }
  }

  /**
   * Delete specific cache entry
   */
  delete(identifier: string = "default"): void {
    try {
      const cacheKey = this.getCacheKey(identifier);
      localStorage.removeItem(cacheKey);
      console.log(`📦 Cache DELETE: ${cacheKey}`);
    } catch (error) {
      console.warn("Failed to delete cache entry:", error);
    }
  }

  /**
   * Force invalidate cache - marks data as stale immediately
   */
  invalidate(identifier: string = "default"): void {
    try {
      const cacheKey = this.getCacheKey(identifier);
      const cachedStr = localStorage.getItem(cacheKey);

      if (cachedStr) {
        const cached: CacheEntry<any> = JSON.parse(cachedStr);
        // Set timestamp to make data appear stale
        cached.timestamp = Date.now() - this.config.freshDuration - 1;
        localStorage.setItem(cacheKey, JSON.stringify(cached));
        console.log(`📦 Cache INVALIDATED: ${cacheKey}`);
      }
    } catch (error) {
      console.warn("Failed to invalidate cache entry:", error);
    }
  }

  /**
   * Check if cache exists and is fresh
   */
  isFresh(identifier: string = "default"): boolean {
    const result = this.get(identifier);
    return result.data !== null && result.isFresh;
  }

  /**
   * Clean up old cache entries to prevent storage quota issues
   */
  cleanup(): void {
    try {
      const keysToDelete: string[] = [];
      const prefix = `${this.storageKey}_`;

      // Find all cache keys for this cache instance
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(prefix)) {
          try {
            const cachedStr = localStorage.getItem(key);
            if (cachedStr) {
              const cached: CacheEntry<any> = JSON.parse(cachedStr);
              if (Date.now() > cached.expiresAt) {
                keysToDelete.push(key);
              }
            }
          } catch {
            // Invalid cache entry, mark for deletion
            keysToDelete.push(key);
          }
        }
      }

      // Delete expired entries
      keysToDelete.forEach((key) => {
        localStorage.removeItem(key);
        console.log(`📦 Cache CLEANUP: ${key}`);
      });

      console.log(
        `📦 Cache cleanup completed: ${keysToDelete.length} entries removed`
      );
    } catch (error) {
      console.warn("Failed to cleanup cache:", error);
    }
  }

  /**
   * Clear all cache entries for this cache instance
   */
  clear(): void {
    try {
      const keysToDelete: string[] = [];
      const prefix = `${this.storageKey}_`;

      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(prefix)) {
          keysToDelete.push(key);
        }
      }

      keysToDelete.forEach((key) => localStorage.removeItem(key));
      console.log(`📦 Cache CLEAR: ${keysToDelete.length} entries removed`);
    } catch (error) {
      console.warn("Failed to clear cache:", error);
    }
  }

  /**
   * Get cache statistics for debugging
   */
  getStats(): {
    totalEntries: number;
    totalSize: number;
    entries: Array<{
      key: string;
      age: number;
      size: number;
      isFresh: boolean;
    }>;
  } {
    const stats = {
      totalEntries: 0,
      totalSize: 0,
      entries: [] as Array<{
        key: string;
        age: number;
        size: number;
        isFresh: boolean;
      }>,
    };

    try {
      const prefix = `${this.storageKey}_`;

      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(prefix)) {
          try {
            const cachedStr = localStorage.getItem(key) || "";
            const cached: CacheEntry<any> = JSON.parse(cachedStr);
            const age = Date.now() - cached.timestamp;
            const isFresh = age < this.config.freshDuration;
            const size = cachedStr.length;

            stats.totalEntries++;
            stats.totalSize += size;
            stats.entries.push({
              key: key.replace(prefix, ""),
              age: Math.round(age / 1000 / 60), // minutes
              size,
              isFresh,
            });
          } catch {
            // Invalid entry, skip
          }
        }
      }
    } catch (error) {
      console.warn("Failed to get cache stats:", error);
    }

    return stats;
  }
}

/**
 * Create a cache instance for today's events
 */
export const createTodaysEventsCache = () => {
  return new SmartCache("todays_events", {
    freshDuration: 30 * 1000, // 30 seconds fresh (much more aggressive)
    maxAge: 5 * 60 * 1000, // 5 minutes max age (shorter for faster updates)
    version: "1.1.0", // Bump version to invalidate old cache
  });
};

/**
 * Global cache invalidation utility for when new data is added
 */
export const invalidateEventsCache = () => {
  const cache = createTodaysEventsCache();

  // Invalidate current date cache
  cache.invalidate("current");

  // Also invalidate any custom date caches that might exist
  const today = new Date().toISOString().split("T")[0];
  cache.invalidate(today);

  console.log(
    "🔄 Events cache invalidated globally - fresh data will be fetched on next request"
  );
};

/**
 * Force refresh events data by clearing cache completely
 */
export const clearEventsCache = () => {
  const cache = createTodaysEventsCache();

  // Clear current date cache
  cache.delete("current");

  // Clear today's date cache
  const today = new Date().toISOString().split("T")[0];
  cache.delete(today);

  console.log("🗑️ Events cache cleared completely - will fetch fresh data");
};

export default SmartCache;
