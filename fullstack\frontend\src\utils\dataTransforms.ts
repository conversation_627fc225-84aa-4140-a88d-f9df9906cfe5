import { EventData } from "./api";

// Utility functions for stat type normalization and formatting
export const normalizeStatType = (statType: string): string => {
  // Convert to lowercase and trim whitespace for database storage
  return statType.trim().toLowerCase();
};

export const formatStatTypeForDisplay = (statType: string): string => {
  // Convert database stat type (lowercase) to proper case for display
  if (!statType) return "Points"; // Default fallback

  const normalized = statType.trim().toLowerCase();

  // Handle common stat type mappings (including legacy uppercase variants)
  const statTypeMap: Record<string, string> = {
    // Lowercase (database format)
    points: "Points",
    assists: "Assists",
    rebounds: "Rebounds",
    blocks: "Blocks",
    steals: "Steals",
    turnovers: "Turnovers",
    pra: "PRA",
    "p+a": "P+A",
    "p+r": "P+R",
    "r+a": "R+A",
    "b+s": "B+S",
    "fg made": "FG Made",
    "fg attempted": "FG Attempted",
    "ft made": "FT Made",
    "ft attempted": "FT Attempted",
    "3-pt's": "3-pt's",
    "3-pt's attempted": "3-pt's Attempted",
    fpts: "Fpts",
    "d rebounds": "D Rebounds",
    "o rebounds": "O Rebounds",
    dunks: "Dunks",
    // Legacy uppercase variants (for backward compatibility)
    POINTS: "Points",
    ASSISTS: "Assists",
    REBOUNDS: "Rebounds",
    BLOCKS: "Blocks",
    STEALS: "Steals",
    TURNOVERS: "Turnovers",
    PRA: "PRA",
    PTS: "Points",
    AST: "Assists",
    REB: "Rebounds",
    // Mixed case variants
    Points: "Points",
    Assists: "Assists",
    Rebounds: "Rebounds",
  };

  // Return mapped value or capitalize first letter as fallback
  return (
    statTypeMap[normalized] ||
    statTypeMap[statType] || // Check original case for legacy data
    statType.charAt(0).toUpperCase() + statType.slice(1).toLowerCase()
  );
};

// Type for the Pick format expected by PicksView
export interface Pick {
  id: number;
  playerName: string;
  playerNumber: string;
  betType: string;
  gameInfo: string;
  confidence: number;
  expertCount: number;
  additionalExperts: number;
  handicapperNames: string[]; // Add handicapper names for expanded view
}

// Helper function to extract player name from event_id
export const extractPlayerName = (eventId: string): string => {
  // Parse event_id pattern: "2025-05-28-NBA-KTOWNS14.0PTS"
  const parts = eventId.split("-");

  if (parts.length >= 5) {
    const playerPart = parts[4]; // "KTOWNS14.0PTS"
    // Remove the stat part (numbers + letters at the end)
    const playerName = playerPart.replace(/\d+(?:\.\d+)?[A-Z]+$/, "");

    // Format common player name patterns
    if (playerName === "KTOWNS") return "K. Towns";
    if (playerName === "THALIBURTON") return "T. Haliburton";
    if (playerName === "JBRUNSON") return "J. Brunson";
    if (playerName === "TESTPLAYER") return "Test Player";

    // Default: just return the extracted name
    return playerName;
  }

  return "Unknown Player";
};

// Helper function to extract player number from event_id or return default
export const extractPlayerNumber = (
  eventId: string,
  playerName: string
): string => {
  // Return jersey numbers for known players
  const name = extractPlayerName(eventId);
  if (name === "K. Towns") return "32";
  if (name === "T. Haliburton") return "0"; // Tyrese Haliburton actually wears #0
  if (name === "J. Brunson") return "11";
  if (name === "Test Player") return "99";

  // Default fallback
  return "0";
};

export const formatBetType = (event: EventData): string => {
  if (event.pick_type && event.stat_threshold && event.team_a && event.team_b) {
    const predictions = event.predictions ?? [];

    if (predictions.length > 0) {
      const overCount = predictions.filter((p) => p === 1).length;
      const underCount = predictions.filter((p) => p === 0).length;
      const direction = overCount > underCount ? event.team_a : event.team_b;

      // Use database stat_type field with proper case formatting
      let statType = "Points"; // Default fallback

      if (event.stat_type) {
        // Use the new utility function to format stat type for display
        statType = formatStatTypeForDisplay(event.stat_type);
      } else {
        // Handle legacy format conversion if stat_type is not available
        const statMatch = event.event_id.match(/([A-Z]+)$/);
        if (statMatch) {
          const stat = statMatch[1];
          if (stat === "PTS") statType = "Points";
          else if (stat === "AST") statType = "Assists";
          else if (stat === "REB") statType = "Rebounds";
          else statType = formatStatTypeForDisplay(stat);
        }
      }

      return `${direction} ${event.stat_threshold} ${statType}`;
    }
  }

  const thresholds = event.stat_thresholds ?? [];
  const predictions = event.predictions ?? [];

  if (thresholds.length > 0 && predictions.length > 0) {
    const threshold = thresholds[0];

    if (threshold === null || threshold === undefined) {
      return "Standard Bet";
    }

    const overCount = predictions.filter((p) => p === 1).length;
    const underCount = predictions.filter((p) => p === 0).length;
    const direction = overCount > underCount ? "Over" : "Under";

    // Use database stat_type field with proper case formatting for legacy fallback
    let statType = "Points"; // Default fallback

    if (event.stat_type) {
      statType = formatStatTypeForDisplay(event.stat_type);
    } else {
      // Legacy parsing from event_id
      const statMatch = event.event_id.match(/([A-Z]+)$/);
      if (statMatch) {
        const stat = statMatch[1];
        if (stat === "PTS") statType = "Points";
        else if (stat === "AST") statType = "Assists";
        else if (stat === "REB") statType = "Rebounds";
        else statType = formatStatTypeForDisplay(stat);
      }
    }

    return `${direction} ${threshold} ${statType}`;
  }
  return "Standard Bet";
};

// Helper function to format game info
export const formatGameInfo = (event: EventData): string => {
  // Extract date from event_id since event_date is null
  const dateParts = event.event_id.split("-");
  let dateStr = "Today";
  let timeStr = "8:00 pm";

  if (dateParts.length >= 3) {
    try {
      const date = new Date(`${dateParts[0]}-${dateParts[1]}-${dateParts[2]}`);
      timeStr = date.toLocaleTimeString("en-US", {
        hour: "numeric",
        minute: "2-digit",
        hour12: true,
      });
      const dayStr = date.toLocaleDateString("en-US", { weekday: "short" });
      dateStr = `${dayStr}, ${timeStr}`;
    } catch (e) {
      // Fallback if date parsing fails
      dateStr = "Today, 8:00 pm";
    }
  }

  // Extract sport from event_id (e.g., "NBA") - it's at index 3, not index 4
  const sport = dateParts.length >= 4 ? dateParts[3] : "NBA";

  // Use database player_name field instead of parsing event_id
  const playerName = event.player_name || extractPlayerName(event.event_id);
  return `${sport} | ${playerName} | ${dateStr}`;
};

export const calculateConfidence = (predictions?: number[]): number => {
  if (!predictions || predictions.length === 0) return 50;

  const agreementCount = predictions.filter((p) => p === predictions[0]).length;
  const agreementRatio = agreementCount / predictions.length;

  return Math.round(50 + agreementRatio * 45);
};

// New function to get actual confidence from stored values
export const getActualConfidence = (confidenceValues?: number[]): number => {
  if (!confidenceValues || confidenceValues.length === 0) return 50;

  // Calculate average confidence from stored values
  const sum = confidenceValues.reduce((acc, conf) => acc + conf, 0);
  const average = sum / confidenceValues.length;

  // Convert from decimal (0.0-1.0) to percentage (0-100) if needed
  const percentage = average <= 1.0 ? average * 100 : average;

  return Math.round(percentage);
};

// Main conversion function to convert EventData to Pick format expected by PicksView
export const convertEventToPick = (event: EventData, index: number): Pick => {
  // Use database fields instead of parsing event_id
  const playerName = event.player_name || extractPlayerName(event.event_id);
  const playerNumber = extractPlayerNumber(event.event_id, playerName);
  const betType = formatBetType(event);
  const gameInfo = formatGameInfo(event);

  // Use actual stored confidence values if available, otherwise fall back to calculated confidence
  const confidence =
    event.confidence_values && event.confidence_values.length > 0
      ? getActualConfidence(event.confidence_values)
      : calculateConfidence(event.predictions);

  const handicappers = event.handicappers ?? [];

  return {
    id: index + 1,
    playerName: playerName,
    playerNumber: playerNumber,
    betType: betType,
    gameInfo: gameInfo,
    confidence: confidence,
    expertCount: Math.min(handicappers.length, 9), // Cap at 9 for display
    additionalExperts: Math.max(0, handicappers.length - 9),
    handicapperNames: handicappers, // Include all handicapper names
  };
};

// Function to convert multiple events to picks
export const convertEventsToPicks = (events: EventData[]): Pick[] => {
  return events.map((event, index) => convertEventToPick(event, index));
};

// Interface for Handicapper data format expected by HandicappersView
export interface HandicapperPick {
  id: number;
  playerName: string;
  playerNumber: string;
  betType: string;
  gameInfo: string;
  confidence?: number;
}

export interface Handicapper {
  id: number;
  name: string;
  sports: string;
  rating: number;
  accuracy: string;
  profileImage: string;
  picks: HandicapperPick[];
}

// Function to convert events to handicappers format
export const convertEventsToHandicappers = (
  events: EventData[]
): Handicapper[] => {
  if (events.length === 0) {
    return [];
  }

  const handicapperMap = new Map<string, EventData[]>();

  events.forEach((event) => {
    const handicapperList = event.handicappers ?? [];
    handicapperList.forEach((handicapperName) => {
      if (!handicapperMap.has(handicapperName)) {
        handicapperMap.set(handicapperName, []);
      }
      handicapperMap.get(handicapperName)!.push(event);
    });
  });

  const handicappers: Handicapper[] = [];

  handicapperMap.forEach((handicapperEvents, handicapperName) => {
    // Use number of events (picks) instead of total predictions for consistency with HandicapperProfile
    const numberOfEvents = handicapperEvents.length;
    const accuracy = Math.min(95, 70 + Math.floor(numberOfEvents * 2));

    const picks: HandicapperPick[] = handicapperEvents.map((event, index) => {
      // Use database fields instead of parsing event_id
      const playerName = event.player_name || extractPlayerName(event.event_id);
      const playerNumber = extractPlayerNumber(event.event_id, playerName);
      const betType = formatBetType(event);
      const gameInfo = formatGameInfo(event);

      // Use actual stored confidence values if available, otherwise fall back to calculated confidence
      const confidence =
        event.confidence_values && event.confidence_values.length > 0
          ? getActualConfidence(event.confidence_values)
          : calculateConfidence(event.predictions);

      return {
        id: index + 1,
        playerName,
        playerNumber,
        betType,
        gameInfo,
        confidence,
      };
    });

    handicappers.push({
      id: generateHandicapperIdFromName(handicapperName),
      name: handicapperName,
      sports: "NBA, NFL, NHL",
      rating: Math.min(5, Math.max(3, Math.floor(accuracy / 20))),
      accuracy: `${accuracy}%`,
      profileImage: `/profile-${handicapperName
        .toLowerCase()
        .replace(/\s+/g, "-")}.jpg`,
      picks,
    });
  });

  return handicappers;
};

export const getHandicapperIdByName = (
  handicapperName: string,
  handicappers: Handicapper[]
): number | null => {
  const handicapper = handicappers.find((h) => h.name === handicapperName);
  return handicapper ? handicapper.id : null;
};

// Generate a deterministic ID from handicapper name
export const generateHandicapperIdFromName = (
  handicapperName: string
): number => {
  let hash = 0;
  for (let i = 0; i < handicapperName.length; i++) {
    const char = handicapperName.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return (Math.abs(hash) % 1000) + 1; // Ensure positive ID between 1-1000
};
